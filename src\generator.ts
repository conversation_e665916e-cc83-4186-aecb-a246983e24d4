import { marked } from 'marked'

interface PortfolioData {
  name: string
  photo: string
  bio: string
  experience: string[]
  education: string[]
  publications: string[]
  projects: string[]
  links: string[]
}

// Sample markdown content (you can replace this with file reading logic)
const sampleMarkdown = `# <PERSON> 👨‍💻

![Profile Photo](https://via.placeholder.com/200x200/4a90e2/ffffff?text=JD)

## Bio 📝

Hello! I'm <PERSON>, a passionate software developer with 5+ years of experience in full-stack development. I love creating innovative solutions and contributing to open-source projects. When I'm not coding, you can find me exploring new technologies, reading tech blogs, or enjoying a good cup of coffee ☕.

## Working Experience 💼

### Senior Software Engineer @ TechCorp Inc.
**2022 - Present**
- Led development of microservices architecture serving 1M+ users
- Implemented CI/CD pipelines reducing deployment time by 60%
- Mentored junior developers and conducted code reviews

### Full Stack Developer @ StartupXYZ
**2020 - 2022**
- Built responsive web applications using React and Node.js
- Designed and implemented RESTful APIs
- Collaborated with cross-functional teams in agile environment

## Education 🎓

### Master of Science in Computer Science
**University of Technology** | 2017 - 2019
- Specialized in Software Engineering and Machine Learning
- GPA: 3.8/4.0

### Bachelor of Science in Information Technology
**State University** | 2013 - 2017
- Graduated Magna Cum Laude
- GPA: 3.7/4.0

## Publications 📚

### "Modern Web Development Practices" (2023)
**Tech Journal Quarterly**
- Co-authored research paper on emerging web development trends

### "Microservices Architecture Patterns" (2022)
**Software Engineering Conference Proceedings**
- Presented at International Software Engineering Conference

## Representative Projects 🚀

### E-Commerce Platform
**Technologies:** React, Node.js, MongoDB, AWS
- Built scalable e-commerce platform handling 10K+ daily transactions
- [GitHub Repository](https://github.com/johndoe/ecommerce-platform)

### Task Management App
**Technologies:** Vue.js, Express.js, PostgreSQL
- Developed collaborative task management application
- [Live Demo](https://taskapp-demo.com) | [GitHub](https://github.com/johndoe/task-manager)

## Links 🔗

- 🐙 [GitHub](https://github.com/johndoe)
- 💼 [LinkedIn](https://linkedin.com/in/johndoe)
- 🐦 [Twitter](https://twitter.com/johndoe)
- 📧 [Email](mailto:<EMAIL>)
- 🌐 [Personal Website](https://johndoe.dev)`

function parseMarkdownToPortfolio(markdown: string): PortfolioData {
  const lines = markdown.split('\n')
  const data: PortfolioData = {
    name: '',
    photo: '',
    bio: '',
    experience: [],
    education: [],
    publications: [],
    projects: [],
    links: []
  }

  let currentSection = ''
  let currentContent: string[] = []

  for (const line of lines) {
    if (line.startsWith('# ')) {
      data.name = line.replace('# ', '').trim()
    } else if (line.startsWith('![Profile Photo]')) {
      const match = line.match(/\((.*?)\)/)
      if (match) data.photo = match[1]
    } else if (line.startsWith('## ')) {
      // Save previous section
      if (currentSection && currentContent.length > 0) {
        const content = currentContent.join('\n').trim()
        switch (currentSection) {
          case 'bio':
            data.bio = content
            break
          case 'experience':
            data.experience = content.split('\n### ').filter(s => s.trim())
            break
          case 'education':
            data.education = content.split('\n### ').filter(s => s.trim())
            break
          case 'publications':
            data.publications = content.split('\n### ').filter(s => s.trim())
            break
          case 'projects':
            data.projects = content.split('\n### ').filter(s => s.trim())
            break
          case 'links':
            data.links = content.split('\n').filter(s => s.trim().startsWith('-'))
            break
        }
      }

      // Start new section
      const sectionTitle = line.replace('## ', '').toLowerCase()
      if (sectionTitle.includes('bio')) currentSection = 'bio'
      else if (sectionTitle.includes('experience')) currentSection = 'experience'
      else if (sectionTitle.includes('education')) currentSection = 'education'
      else if (sectionTitle.includes('publication')) currentSection = 'publications'
      else if (sectionTitle.includes('project')) currentSection = 'projects'
      else if (sectionTitle.includes('link')) currentSection = 'links'
      else currentSection = ''
      
      currentContent = []
    } else if (currentSection && line.trim()) {
      currentContent.push(line)
    }
  }

  // Handle last section
  if (currentSection && currentContent.length > 0) {
    const content = currentContent.join('\n').trim()
    switch (currentSection) {
      case 'bio':
        data.bio = content
        break
      case 'experience':
        data.experience = content.split('\n### ').filter(s => s.trim())
        break
      case 'education':
        data.education = content.split('\n### ').filter(s => s.trim())
        break
      case 'publications':
        data.publications = content.split('\n### ').filter(s => s.trim())
        break
      case 'projects':
        data.projects = content.split('\n### ').filter(s => s.trim())
        break
      case 'links':
        data.links = content.split('\n').filter(s => s.trim().startsWith('-'))
        break
    }
  }

  return data
}

function generateHTML(data: PortfolioData): string {
  const experienceHTML = data.experience.map(exp => `
    <div class="experience-item">
      ${marked(exp)}
    </div>
  `).join('')

  const educationHTML = data.education.map(edu => `
    <div class="education-item">
      ${marked(edu)}
    </div>
  `).join('')

  const publicationsHTML = data.publications.map(pub => `
    <div class="publication-item">
      ${marked(pub)}
    </div>
  `).join('')

  const projectsHTML = data.projects.map(proj => `
    <div class="project-item">
      ${marked(proj)}
    </div>
  `).join('')

  const linksHTML = data.links.map(link => `
    <div class="link-item">
      ${marked(link)}
    </div>
  `).join('')

  return `
    <div class="portfolio-container">
      <header class="hero-section">
        <div class="profile-photo">
          <img src="${data.photo}" alt="${data.name}" />
        </div>
        <h1 class="name">${data.name}</h1>
      </header>

      <section class="bio-section">
        <h2>Bio 📝</h2>
        <div class="bio-content">
          ${marked(data.bio)}
        </div>
      </section>

      <section class="experience-section">
        <h2>Working Experience 💼</h2>
        <div class="experience-content">
          ${experienceHTML}
        </div>
      </section>

      <section class="education-section">
        <h2>Education 🎓</h2>
        <div class="education-content">
          ${educationHTML}
        </div>
      </section>

      <section class="publications-section">
        <h2>Publications 📚</h2>
        <div class="publications-content">
          ${publicationsHTML}
        </div>
      </section>

      <section class="projects-section">
        <h2>Representative Projects 🚀</h2>
        <div class="projects-content">
          ${projectsHTML}
        </div>
      </section>

      <section class="links-section">
        <h2>Links 🔗</h2>
        <div class="links-content">
          ${linksHTML}
        </div>
      </section>
    </div>
  `
}

export function generatePortfolio(): void {
  const portfolioData = parseMarkdownToPortfolio(sampleMarkdown)
  const htmlContent = generateHTML(portfolioData)
  
  const appElement = document.querySelector('#app')
  if (appElement) {
    appElement.innerHTML = htmlContent
  }
}
