import { marked } from 'marked'

interface PortfolioData {
  name: string
  photo: string
  bio: string
  experience: string[]
  education: string[]
  publications: string[]
  projects: string[]
  links: string[]
}

// Function to read markdown content from file
async function readMarkdownFile(filePath: string = 'template.md'): Promise<string> {
  try {
    const response = await fetch(filePath)
    if (!response.ok) {
      throw new Error(`Failed to fetch ${filePath}: ${response.statusText}`)
    }
    return await response.text()
  } catch (error) {
    console.error('Error reading markdown file:', error)
    // Fallback to default content if file reading fails
    return `# Portfolio

![Profile Photo](https://via.placeholder.com/150x150/000080/ffffff?text=Portfolio)

## Bio 📝

Welcome to my portfolio! Please update the template.md file with your information.

## Working Experience 💼

### Your Job Title @ Company Name
**Date Range**
- Add your experience details here

## Education 🎓

### Your Degree @ University Name
**Date Range**
- Add your education details here

## Publications 📚

### "Your Publication Title" (Year)
**Journal/Conference Name**
- Add publication details here

## Representative Projects 🚀

### Project Name
**Technologies:** List your technologies
- Add project description here
- [Project Link](https://example.com)

## Links 🔗

- 🐙 [GitHub](https://github.com/yourusername)
- 💼 [LinkedIn](https://linkedin.com/in/yourusername)
- 📧 [Email](mailto:<EMAIL>)`
  }
}

function parseMarkdownToPortfolio(markdown: string): PortfolioData {
  const lines = markdown.split('\n')
  const data: PortfolioData = {
    name: '',
    photo: '',
    bio: '',
    experience: [],
    education: [],
    publications: [],
    projects: [],
    links: [],
  }

  let currentSection = ''
  let currentContent: string[] = []

  for (const line of lines) {
    if (line.startsWith('# ')) {
      data.name = line.replace('# ', '').trim()
    } else if (line.startsWith('![Profile Photo]')) {
      const match = line.match(/\((.*?)\)/)
      if (match) data.photo = match[1]
    } else if (line.startsWith('## ')) {
      // Save previous section
      if (currentSection && currentContent.length > 0) {
        const content = currentContent.join('\n').trim()
        switch (currentSection) {
          case 'bio':
            data.bio = content
            break
          case 'experience':
            data.experience = content.split('\n### ').filter((s) => s.trim())
            break
          case 'education':
            data.education = content.split('\n### ').filter((s) => s.trim())
            break
          case 'publications':
            data.publications = content.split('\n### ').filter((s) => s.trim())
            break
          case 'projects':
            data.projects = content.split('\n### ').filter((s) => s.trim())
            break
          case 'links':
            data.links = content.split('\n').filter((s) => s.trim().startsWith('-'))
            break
        }
      }

      // Start new section
      const sectionTitle = line.replace('## ', '').toLowerCase()
      if (sectionTitle.includes('bio')) currentSection = 'bio'
      else if (sectionTitle.includes('experience')) currentSection = 'experience'
      else if (sectionTitle.includes('education')) currentSection = 'education'
      else if (sectionTitle.includes('publication')) currentSection = 'publications'
      else if (sectionTitle.includes('project')) currentSection = 'projects'
      else if (sectionTitle.includes('link')) currentSection = 'links'
      else currentSection = ''

      currentContent = []
    } else if (currentSection && line.trim()) {
      currentContent.push(line)
    }
  }

  // Handle last section
  if (currentSection && currentContent.length > 0) {
    const content = currentContent.join('\n').trim()
    switch (currentSection) {
      case 'bio':
        data.bio = content
        break
      case 'experience':
        data.experience = content.split('\n### ').filter((s) => s.trim())
        break
      case 'education':
        data.education = content.split('\n### ').filter((s) => s.trim())
        break
      case 'publications':
        data.publications = content.split('\n### ').filter((s) => s.trim())
        break
      case 'projects':
        data.projects = content.split('\n### ').filter((s) => s.trim())
        break
      case 'links':
        data.links = content.split('\n').filter((s) => s.trim().startsWith('-'))
        break
    }
  }

  return data
}

function generateHTML(data: PortfolioData): string {
  const experienceHTML = data.experience
    .map(
      (exp) => `
    <div class="experience-item">
      ${marked(exp)}
    </div>
  `
    )
    .join('')

  const educationHTML = data.education
    .map(
      (edu) => `
    <div class="education-item">
      ${marked(edu)}
    </div>
  `
    )
    .join('')

  const publicationsHTML = data.publications
    .map(
      (pub) => `
    <div class="publication-item">
      ${marked(pub)}
    </div>
  `
    )
    .join('')

  const projectsHTML = data.projects
    .map(
      (proj) => `
    <div class="project-item">
      ${marked(proj)}
    </div>
  `
    )
    .join('')

  const linksHTML = data.links
    .map(
      (link) => `
    <div class="link-item">
      ${marked(link)}
    </div>
  `
    )
    .join('')

  return `
    <div class="portfolio-container">
      <header class="hero-section">
        <div class="profile-photo">
          <img src="${data.photo}" alt="${data.name}" />
        </div>
        <h1 class="name">${data.name}</h1>
      </header>

      <section class="bio-section">
        <h2>Bio 📝</h2>
        <div class="bio-content">
          ${marked(data.bio)}
        </div>
      </section>

      <section class="experience-section">
        <h2>Working Experience 💼</h2>
        <div class="experience-content">
          ${experienceHTML}
        </div>
      </section>

      <section class="education-section">
        <h2>Education 🎓</h2>
        <div class="education-content">
          ${educationHTML}
        </div>
      </section>

      <section class="publications-section">
        <h2>Publications 📚</h2>
        <div class="publications-content">
          ${publicationsHTML}
        </div>
      </section>

      <section class="projects-section">
        <h2>Representative Projects 🚀</h2>
        <div class="projects-content">
          ${projectsHTML}
        </div>
      </section>

      <section class="links-section">
        <h2>Links 🔗</h2>
        <div class="links-content">
          ${linksHTML}
        </div>
      </section>
    </div>
  `
}

export async function generatePortfolio(
  markdownPath: string = 'template.md'
): Promise<void> {
  try {
    const markdownContent = await readMarkdownFile(markdownPath)
    const portfolioData = parseMarkdownToPortfolio(markdownContent)
    const htmlContent = generateHTML(portfolioData)

    const appElement = document.querySelector('#app')
    if (appElement) {
      appElement.innerHTML = htmlContent
    }
  } catch (error) {
    console.error('Error generating portfolio:', error)
    const appElement = document.querySelector('#app')
    if (appElement) {
      appElement.innerHTML =
        '<div class="portfolio-container"><h1>Error loading portfolio</h1><p>Please check that template.md exists and is accessible.</p></div>'
    }
  }
}
