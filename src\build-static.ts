import { marked } from 'marked'
import * as fs from 'fs'
import * as path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

interface PortfolioData {
  name: string
  photo: string
  bio: string
  experience: string[]
  education: string[]
  publications: string[]
  projects: string[]
  links: string[]
}

function parseMarkdownToPortfolio(markdown: string): PortfolioData {
  const lines = markdown.split('\n')
  const data: PortfolioData = {
    name: '',
    photo: '',
    bio: '',
    experience: [],
    education: [],
    publications: [],
    projects: [],
    links: [],
  }

  let currentSection = ''
  let currentContent: string[] = []

  for (const line of lines) {
    if (line.startsWith('# ')) {
      data.name = line.replace('# ', '').trim()
    } else if (line.startsWith('![Profile Photo]')) {
      const match = line.match(/\((.*?)\)/)
      if (match) data.photo = match[1]
    } else if (line.startsWith('## ')) {
      // Save previous section
      if (currentSection && currentContent.length > 0) {
        const content = currentContent.join('\n').trim()
        switch (currentSection) {
          case 'bio':
            data.bio = content
            break
          case 'experience':
            data.experience = content.split('\n### ').filter((s) => s.trim())
            break
          case 'education':
            data.education = content.split('\n### ').filter((s) => s.trim())
            break
          case 'publications':
            data.publications = content.split('\n### ').filter((s) => s.trim())
            break
          case 'projects':
            data.projects = content.split('\n### ').filter((s) => s.trim())
            break
          case 'links':
            data.links = content.split('\n').filter((s) => s.trim().startsWith('-'))
            break
        }
      }

      // Start new section
      const sectionTitle = line.replace('## ', '').toLowerCase()
      if (sectionTitle.includes('bio')) currentSection = 'bio'
      else if (sectionTitle.includes('experience')) currentSection = 'experience'
      else if (sectionTitle.includes('education')) currentSection = 'education'
      else if (sectionTitle.includes('publication')) currentSection = 'publications'
      else if (sectionTitle.includes('project')) currentSection = 'projects'
      else if (sectionTitle.includes('link')) currentSection = 'links'
      else currentSection = ''

      currentContent = []
    } else if (currentSection && line.trim()) {
      currentContent.push(line)
    }
  }

  // Handle last section
  if (currentSection && currentContent.length > 0) {
    const content = currentContent.join('\n').trim()
    switch (currentSection) {
      case 'bio':
        data.bio = content
        break
      case 'experience':
        data.experience = content.split('\n### ').filter((s) => s.trim())
        break
      case 'education':
        data.education = content.split('\n### ').filter((s) => s.trim())
        break
      case 'publications':
        data.publications = content.split('\n### ').filter((s) => s.trim())
        break
      case 'projects':
        data.projects = content.split('\n### ').filter((s) => s.trim())
        break
      case 'links':
        data.links = content.split('\n').filter((s) => s.trim().startsWith('-'))
        break
    }
  }

  return data
}

function generateHTML(data: PortfolioData, css: string): string {
  const experienceHTML = data.experience
    .map(
      (exp) => `
    <div class="experience-item">
      ${marked(exp)}
    </div>
  `
    )
    .join('')

  const educationHTML = data.education
    .map(
      (edu) => `
    <div class="education-item">
      ${marked(edu)}
    </div>
  `
    )
    .join('')

  const publicationsHTML = data.publications
    .map(
      (pub) => `
    <div class="publication-item">
      ${marked(pub)}
    </div>
  `
    )
    .join('')

  const projectsHTML = data.projects
    .map(
      (proj) => `
    <div class="project-item">
      ${marked(proj)}
    </div>
  `
    )
    .join('')

  const linksHTML = data.links
    .map(
      (link) => `
    <div class="link-item">
      ${marked(link)}
    </div>
  `
    )
    .join('')

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.name} - Portfolio</title>
    <style>
        ${css}
    </style>
</head>
<body>
    <div id="app">
        <div class="portfolio-container">
            <header class="hero-section">
                <div class="profile-photo">
                    <img src="${data.photo}" alt="${data.name}" />
                </div>
                <h1 class="name">${data.name}</h1>
            </header>

            <section class="bio-section">
                <h2>Bio 📝</h2>
                <div class="bio-content">
                    ${marked(data.bio)}
                </div>
            </section>

            <section class="experience-section">
                <h2>Working Experience 💼</h2>
                <div class="experience-content">
                    ${experienceHTML}
                </div>
            </section>

            <section class="education-section">
                <h2>Education 🎓</h2>
                <div class="education-content">
                    ${educationHTML}
                </div>
            </section>

            <section class="publications-section">
                <h2>Publications 📚</h2>
                <div class="publications-content">
                    ${publicationsHTML}
                </div>
            </section>

            <section class="projects-section">
                <h2>Representative Projects 🚀</h2>
                <div class="projects-content">
                    ${projectsHTML}
                </div>
            </section>

            <section class="links-section">
                <h2>Links 🔗</h2>
                <div class="links-content">
                    ${linksHTML}
                </div>
            </section>
        </div>
    </div>
</body>
</html>`
}

// Build function
export function buildStaticSite(markdownPath: string, outputPath: string): void {
  try {
    // Read markdown file
    const markdownContent = fs.readFileSync(markdownPath, 'utf-8')

    // Read CSS file
    const cssPath = path.join(__dirname, '..', 'src', 'style.css')
    const cssContent = fs.readFileSync(cssPath, 'utf-8')

    // Parse markdown to portfolio data
    const portfolioData = parseMarkdownToPortfolio(markdownContent)

    // Generate HTML
    const htmlContent = generateHTML(portfolioData, cssContent)

    // Write output file
    fs.writeFileSync(outputPath, htmlContent, 'utf-8')

    console.log(`✅ Static portfolio generated successfully at: ${outputPath}`)
  } catch (error) {
    console.error('❌ Error generating static portfolio:', error)
  }
}

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2)
  const markdownPath = args[0] || 'template.md'
  const outputPath = args[1] || 'portfolio.html'

  buildStaticSite(markdownPath, outputPath)
}
