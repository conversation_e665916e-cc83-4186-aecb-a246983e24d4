# 🚀 Retro Portfolio Generator

A **VERY SIMPLE** static site generator that converts a Markdown file into a beautiful retro-styled portfolio HTML page. Built with TypeScript, Vite, and pure HTML+CSS+JS - no frameworks, no component libraries, just pure web technologies with a nostalgic 80s/90s aesthetic! 🎮

## ✨ Features

- 📝 **Markdown to HTML**: Convert structured markdown into a complete portfolio
- 🎨 **Retro Styling**: Terminal-inspired design with neon colors and scanline effects
- 📱 **Responsive Design**: Works perfectly on both desktop and mobile devices
- ⚡ **Pure Web Tech**: No React, Vue, or other frameworks - just HTML, CSS, and TypeScript
- 🎯 **Single File Output**: Generates one complete HTML file with embedded CSS
- 🔧 **Easy to Use**: Simple CLI commands to generate your portfolio

## 🎨 Design Features

- **Neon color scheme**: Green, cyan, amber, pink, and purple
- **Scanline effects**: Retro CRT monitor simulation
- **Glowing borders**: Animated neon glow effects
- **Monospace fonts**: Terminal-style typography
- **Hover animations**: Interactive elements with smooth transitions
- **Responsive grid**: Adapts beautifully to different screen sizes

## 📋 Portfolio Sections

The generator supports these portfolio sections:

- 👤 **Name & Photo**: Profile picture and name
- 📝 **Bio**: Personal introduction and summary
- 💼 **Working Experience**: Professional experience with details
- 🎓 **Education**: Academic background and achievements
- 📚 **Publications**: Research papers, articles, and publications
- 🚀 **Representative Projects**: Showcase of key projects
- 🔗 **Links**: Social media and professional links

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pnpm install
```

### 2. Development Mode
Run the development server to see live preview:
```bash
pnpm dev
```
Open http://localhost:5173 in your browser.

### 3. Generate Static Portfolio
Create your static HTML file:
```bash
pnpm generate
```
This will generate `portfolio.html` from `template.md`.

### 4. Custom Markdown File
Generate from your own markdown file:
```bash
pnpm build-static
node generate.js your-portfolio.md output.html
```

## 📝 Markdown Template

Use the provided `template.md` as a starting point. The structure should follow this format:

```markdown
# Your Name 👨‍💻

![Profile Photo](your-photo-url.jpg)

## Bio 📝
Your personal introduction...

## Working Experience 💼
### Job Title @ Company
**Date Range**
- Achievement 1
- Achievement 2

## Education 🎓
### Degree @ University
**Date Range**
- Details about your education

## Publications 📚
### "Paper Title" (Year)
**Journal/Conference**
- Description of the publication

## Representative Projects 🚀
### Project Name
**Technologies:** List of technologies
- Project description
- [Link](https://github.com/...)

## Links 🔗
- 🐙 [GitHub](https://github.com/username)
- 💼 [LinkedIn](https://linkedin.com/in/username)
- 📧 [Email](mailto:<EMAIL>)
```

## 🛠️ Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build the Vite project
- `pnpm preview` - Preview the built project
- `pnpm build-static` - Compile TypeScript for static generation
- `pnpm generate` - Generate portfolio.html from template.md

## 📁 Project Structure

```
simsite/
├── src/
│   ├── main.ts          # Main application entry
│   ├── generator.ts     # Portfolio generator for dev mode
│   ├── build-static.ts  # Static site generator
│   └── style.css        # Retro styling
├── template.md          # Example markdown template
├── generate.js          # CLI script for generation
├── portfolio.html       # Generated static portfolio
└── README.md           # This file
```

## 🎨 Customization

### Colors
Edit the CSS variables in `src/style.css`:
```css
:root {
  --retro-green: #00ff41;
  --retro-amber: #ffb000;
  --retro-cyan: #00ffff;
  --retro-pink: #ff1493;
  --retro-purple: #9932cc;
}
```

### Fonts
Change the font family:
```css
--retro-font: 'Courier New', 'Monaco', 'Menlo', monospace;
```

### Effects
Modify animations and effects in the CSS file to match your style preferences.

## 🌟 Example Output

The generator creates a fully self-contained HTML file with:
- Embedded CSS (no external dependencies)
- Responsive design
- Retro terminal aesthetic
- Smooth animations and hover effects
- Professional portfolio layout

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve this retro portfolio generator!

---

**Made with ❤️ and lots of ☕ for the retro computing enthusiasts!** 🎮✨
