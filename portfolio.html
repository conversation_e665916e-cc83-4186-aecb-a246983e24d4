<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> 👨‍💻 - Portfolio</title>
    <style>
        /* Classic 90s/2000s Portfolio Styles */
body {
  font-family: 'Times New Roman', Times, serif;
  background-color: #ffffff;
  color: #000000;
  margin: 0;
  padding: 20px;
  line-height: 1.4;
  font-size: 14px;
}

#app {
  max-width: 800px;
  margin: 0 auto;
  background-color: #ffffff;
}

.portfolio-container {
  border: 2px solid #000080;
  padding: 20px;
  background-color: #f8f8f8;
}

/* Header Section */
.hero-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #e6e6fa;
  border: 1px solid #9370db;
}

.profile-photo img {
  width: 150px;
  height: 150px;
  border: 3px solid #000080;
  margin-bottom: 15px;
}

.name {
  font-size: 28px;
  color: #000080;
  font-weight: bold;
  margin: 10px 0;
  text-decoration: underline;
}

/* Section Styles */
section {
  margin-bottom: 25px;
  padding: 15px;
  border: 1px solid #cccccc;
  background-color: #ffffff;
}

section h2 {
  color: #800000;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 2px solid #800000;
  text-transform: uppercase;
}

/* Bio Section */
.bio-content {
  font-size: 16px;
  text-align: justify;
  padding: 15px;
  background-color: #fffacd;
  border: 1px dashed #daa520;
  margin: 10px 0;
}

/* Content Items */
.experience-item,
.education-item,
.publication-item,
.project-item {
  margin-bottom: 20px;
  border: 1px solid #808080;
  background-color: #f0f0f0;
  padding: 10px;
}

.experience-item h3,
.education-item h3,
.publication-item h3,
.project-item h3 {
  color: #000080;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  text-decoration: underline;
}

/* Lists */
ul {
  margin-left: 30px;
  margin-bottom: 10px;
}

li {
  margin-bottom: 5px;
  list-style-type: disc;
}

ol {
  margin-left: 30px;
  margin-bottom: 10px;
}

/* Links Section */
.links-content {
  background-color: #e0e0e0;
  border: 2px solid #808080;
  padding: 15px;
}

.link-item {
  display: inline-block;
  margin: 5px 10px;
  padding: 8px 12px;
  background-color: #d3d3d3;
  border: 1px outset #c0c0c0;
  text-align: center;
  min-width: 120px;
}

.link-item:hover {
  background-color: #b0b0b0;
  border: 1px inset #c0c0c0;
}

/* Links and Typography */
a {
  color: #0000ee;
  text-decoration: underline;
}

a:visited {
  color: #551a8b;
}

a:hover {
  color: #ff0000;
  text-decoration: none;
}

p {
  margin-bottom: 10px;
  text-align: justify;
}

strong {
  font-weight: bold;
  color: #000000;
}

em {
  font-style: italic;
}

/* Table Styles for structured content */
table {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
  border: 1px solid #000000;
}

th,
td {
  border: 1px solid #808080;
  padding: 8px;
  text-align: left;
}

th {
  background-color: #c0c0c0;
  font-weight: bold;
}

tr:nth-child(even) {
  background-color: #f0f0f0;
}

/* Classic button style */
.classic-button {
  background-color: #c0c0c0;
  border: 2px outset #c0c0c0;
  padding: 4px 8px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  cursor: pointer;
}

.classic-button:hover {
  background-color: #d0d0d0;
}

.classic-button:active {
  border: 2px inset #c0c0c0;
}

/* Responsive Design for mobile */
@media (max-width: 600px) {
  body {
    padding: 10px;
    font-size: 12px;
  }

  #app {
    max-width: 100%;
  }

  .portfolio-container {
    padding: 15px;
  }

  .name {
    font-size: 22px;
  }

  .profile-photo img {
    width: 120px;
    height: 120px;
  }

  section {
    padding: 10px;
  }

  section h2 {
    font-size: 16px;
  }

  .link-item {
    display: block;
    margin: 5px 0;
    min-width: auto;
  }
}

/* Print styles */
@media print {
  body {
    background-color: white;
    color: black;
    font-size: 12px;
  }

  .portfolio-container {
    border: 1px solid black;
    box-shadow: none;
  }

  .hero-section {
    background-color: white;
    border: 1px solid black;
  }

  section {
    background-color: white;
    border: 1px solid black;
  }

  a {
    color: black;
    text-decoration: underline;
  }
}

    </style>
</head>
<body>
    <div id="app">
        <div class="portfolio-container">
            <header class="hero-section">
                <div class="profile-photo">
                    <img src="https://via.placeholder.com/150x150/000080/ffffff?text=JD" alt="John Doe 👨‍💻" />
                </div>
                <h1 class="name">John Doe 👨‍💻</h1>
            </header>

            <section class="bio-section">
                <h2>Bio 📝</h2>
                <div class="bio-content">
                    <p>Welcome to my personal homepage! I&#39;m John Doe, a dedicated software engineer with over 5 years of experience in the technology industry. I specialize in full-stack web development and have a passion for creating robust, scalable applications.
My expertise includes working with various programming languages and frameworks, and I enjoy tackling complex technical challenges. When I&#39;m not programming, I like to stay updated with the latest industry trends and contribute to open-source projects.</p>

                </div>
            </section>

            <section class="experience-section">
                <h2>Working Experience 💼</h2>
                <div class="experience-content">
                    
    <div class="experience-item">
      <h3>Senior Software Engineer @ TechCorp Inc.</h3>
<p><strong>2022 - Present</strong></p>
<ul>
<li>Led development of microservices architecture serving 1M+ users</li>
<li>Implemented CI/CD pipelines reducing deployment time by 60%</li>
<li>Mentored junior developers and conducted code reviews</li>
</ul>

    </div>
  
    <div class="experience-item">
      <p>Full Stack Developer @ StartupXYZ
<strong>2020 - 2022</strong></p>
<ul>
<li>Built responsive web applications using React and Node.js</li>
<li>Designed and implemented RESTful APIs</li>
<li>Collaborated with cross-functional teams in agile environment</li>
</ul>

    </div>
  
    <div class="experience-item">
      <p>Junior Developer @ WebSolutions Ltd.
<strong>2019 - 2020</strong></p>
<ul>
<li>Developed and maintained client websites using HTML, CSS, and JavaScript</li>
<li>Worked with databases (MySQL, PostgreSQL)</li>
<li>Participated in requirement gathering and project planning</li>
</ul>

    </div>
  
                </div>
            </section>

            <section class="education-section">
                <h2>Education 🎓</h2>
                <div class="education-content">
                    
    <div class="education-item">
      <h3>Master of Science in Computer Science</h3>
<p><strong>University of Technology</strong> | 2017 - 2019</p>
<ul>
<li>Specialized in Software Engineering and Machine Learning</li>
<li>GPA: 3.8/4.0</li>
<li>Thesis: &quot;Optimizing Database Query Performance using Machine Learning&quot;</li>
</ul>

    </div>
  
    <div class="education-item">
      <p>Bachelor of Science in Information Technology
<strong>State University</strong> | 2013 - 2017</p>
<ul>
<li>Graduated Magna Cum Laude</li>
<li>GPA: 3.7/4.0</li>
<li>Relevant Coursework: Data Structures, Algorithms, Database Systems</li>
</ul>

    </div>
  
                </div>
            </section>

            <section class="publications-section">
                <h2>Publications 📚</h2>
                <div class="publications-content">
                    
    <div class="publication-item">
      <h3>&quot;Modern Web Development Practices&quot; (2023)</h3>
<p><strong>Tech Journal Quarterly</strong></p>
<ul>
<li>Co-authored research paper on emerging web development trends</li>
<li>Cited by 50+ academic papers</li>
</ul>

    </div>
  
    <div class="publication-item">
      <p>&quot;Microservices Architecture Patterns&quot; (2022)
<strong>Software Engineering Conference Proceedings</strong></p>
<ul>
<li>Presented at International Software Engineering Conference</li>
<li>Available on IEEE Xplore</li>
</ul>

    </div>
  
                </div>
            </section>

            <section class="projects-section">
                <h2>Representative Projects 🚀</h2>
                <div class="projects-content">
                    
    <div class="project-item">
      <h3>E-Commerce Platform</h3>
<p><strong>Technologies:</strong> React, Node.js, MongoDB, AWS</p>
<ul>
<li>Built scalable e-commerce platform handling 10K+ daily transactions</li>
<li>Implemented real-time inventory management and payment processing</li>
<li><a href="https://github.com/johndoe/ecommerce-platform">GitHub Repository</a></li>
</ul>

    </div>
  
    <div class="project-item">
      <p>Task Management App
<strong>Technologies:</strong> Vue.js, Express.js, PostgreSQL</p>
<ul>
<li>Developed collaborative task management application</li>
<li>Features include real-time updates, file sharing, and team collaboration</li>
<li><a href="https://taskapp-demo.com">Live Demo</a> | <a href="https://github.com/johndoe/task-manager">GitHub</a></li>
</ul>

    </div>
  
    <div class="project-item">
      <p>Open Source Library: DataViz
<strong>Technologies:</strong> TypeScript, D3.js, Canvas API</p>
<ul>
<li>Created lightweight data visualization library</li>
<li>500+ GitHub stars, 50+ contributors</li>
<li><a href="https://npmjs.com/package/dataviz-lib">NPM Package</a> | <a href="https://dataviz-docs.com">Documentation</a></li>
</ul>

    </div>
  
                </div>
            </section>

            <section class="links-section">
                <h2>Links 🔗</h2>
                <div class="links-content">
                    
    <div class="link-item">
      <ul>
<li>🐙 <a href="https://github.com/johndoe">GitHub</a></li>
</ul>

    </div>
  
    <div class="link-item">
      <ul>
<li>💼 <a href="https://linkedin.com/in/johndoe">LinkedIn</a></li>
</ul>

    </div>
  
    <div class="link-item">
      <ul>
<li>🐦 <a href="https://twitter.com/johndoe">Twitter</a></li>
</ul>

    </div>
  
    <div class="link-item">
      <ul>
<li>📧 <a href="mailto:<EMAIL>">Email</a></li>
</ul>

    </div>
  
    <div class="link-item">
      <ul>
<li>🌐 <a href="https://johndoe.dev">Personal Website</a></li>
</ul>

    </div>
  
    <div class="link-item">
      <ul>
<li>📝 <a href="https://johndoe.blog">Blog</a></li>
</ul>

    </div>
  
                </div>
            </section>
        </div>
    </div>
</body>
</html>