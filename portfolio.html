<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> 👨‍💻 - Portfolio</title>
    <style>
        /* Retro Portfolio Styles */
:root {
  --retro-green: #00ff41;
  --retro-amber: #ffb000;
  --retro-cyan: #00ffff;
  --retro-pink: #ff1493;
  --retro-purple: #9932cc;
  --dark-bg: #0a0a0a;
  --darker-bg: #050505;
  --terminal-green: #00ff00;
  --retro-font: 'Courier New', 'Monaco', 'Menlo', monospace;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--retro-font);
  background: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 100%);
  color: var(--retro-green);
  line-height: 1.6;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Retro scanlines effect */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 255, 65, 0.03) 2px,
    rgba(0, 255, 65, 0.03) 4px
  );
  pointer-events: none;
  z-index: 1000;
}

#app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
}

.portfolio-container {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--retro-green);
  border-radius: 10px;
  box-shadow:
    0 0 20px var(--retro-green),
    inset 0 0 20px rgba(0, 255, 65, 0.1);
  padding: 2rem;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow:
      0 0 20px var(--retro-green),
      inset 0 0 20px rgba(0, 255, 65, 0.1);
  }
  to {
    box-shadow:
      0 0 30px var(--retro-green),
      0 0 40px var(--retro-green),
      inset 0 0 20px rgba(0, 255, 65, 0.2);
  }
}

/* Hero Section */
.hero-section {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  border-bottom: 2px solid var(--retro-cyan);
}

.profile-photo img {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  border: 3px solid var(--retro-amber);
  box-shadow: 0 0 20px var(--retro-amber);
  margin-bottom: 1rem;
  filter: sepia(20%) hue-rotate(90deg);
}

.name {
  font-size: 3rem;
  color: var(--retro-cyan);
  text-shadow: 0 0 10px var(--retro-cyan);
  margin-bottom: 1rem;
  animation: flicker 3s infinite;
}

@keyframes flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

    </style>
</head>
<body>
    <div id="app">
        <div class="portfolio-container">
            <header class="hero-section">
                <div class="profile-photo">
                    <img src="https://via.placeholder.com/200x200/4a90e2/ffffff?text=JD" alt="John Doe 👨‍💻" />
                </div>
                <h1 class="name">John Doe 👨‍💻</h1>
            </header>

            <section class="bio-section">
                <h2>Bio 📝</h2>
                <div class="bio-content">
                    <p>Hello! I&#39;m John Doe, a passionate software developer with 5+ years of experience in full-stack development. I love creating innovative solutions and contributing to open-source projects. When I&#39;m not coding, you can find me exploring new technologies, reading tech blogs, or enjoying a good cup of coffee ☕.</p>

                </div>
            </section>

            <section class="experience-section">
                <h2>Working Experience 💼</h2>
                <div class="experience-content">
                    
    <div class="experience-item">
      <h3>Senior Software Engineer @ TechCorp Inc.</h3>
<p><strong>2022 - Present</strong></p>
<ul>
<li>Led development of microservices architecture serving 1M+ users</li>
<li>Implemented CI/CD pipelines reducing deployment time by 60%</li>
<li>Mentored junior developers and conducted code reviews</li>
</ul>

    </div>
  
    <div class="experience-item">
      <p>Full Stack Developer @ StartupXYZ
<strong>2020 - 2022</strong></p>
<ul>
<li>Built responsive web applications using React and Node.js</li>
<li>Designed and implemented RESTful APIs</li>
<li>Collaborated with cross-functional teams in agile environment</li>
</ul>

    </div>
  
    <div class="experience-item">
      <p>Junior Developer @ WebSolutions Ltd.
<strong>2019 - 2020</strong></p>
<ul>
<li>Developed and maintained client websites using HTML, CSS, and JavaScript</li>
<li>Worked with databases (MySQL, PostgreSQL)</li>
<li>Participated in requirement gathering and project planning</li>
</ul>

    </div>
  
                </div>
            </section>

            <section class="education-section">
                <h2>Education 🎓</h2>
                <div class="education-content">
                    
    <div class="education-item">
      <h3>Master of Science in Computer Science</h3>
<p><strong>University of Technology</strong> | 2017 - 2019</p>
<ul>
<li>Specialized in Software Engineering and Machine Learning</li>
<li>GPA: 3.8/4.0</li>
<li>Thesis: &quot;Optimizing Database Query Performance using Machine Learning&quot;</li>
</ul>

    </div>
  
    <div class="education-item">
      <p>Bachelor of Science in Information Technology
<strong>State University</strong> | 2013 - 2017</p>
<ul>
<li>Graduated Magna Cum Laude</li>
<li>GPA: 3.7/4.0</li>
<li>Relevant Coursework: Data Structures, Algorithms, Database Systems</li>
</ul>

    </div>
  
                </div>
            </section>

            <section class="publications-section">
                <h2>Publications 📚</h2>
                <div class="publications-content">
                    
    <div class="publication-item">
      <h3>&quot;Modern Web Development Practices&quot; (2023)</h3>
<p><strong>Tech Journal Quarterly</strong></p>
<ul>
<li>Co-authored research paper on emerging web development trends</li>
<li>Cited by 50+ academic papers</li>
</ul>

    </div>
  
    <div class="publication-item">
      <p>&quot;Microservices Architecture Patterns&quot; (2022)
<strong>Software Engineering Conference Proceedings</strong></p>
<ul>
<li>Presented at International Software Engineering Conference</li>
<li>Available on IEEE Xplore</li>
</ul>

    </div>
  
                </div>
            </section>

            <section class="projects-section">
                <h2>Representative Projects 🚀</h2>
                <div class="projects-content">
                    
    <div class="project-item">
      <h3>E-Commerce Platform</h3>
<p><strong>Technologies:</strong> React, Node.js, MongoDB, AWS</p>
<ul>
<li>Built scalable e-commerce platform handling 10K+ daily transactions</li>
<li>Implemented real-time inventory management and payment processing</li>
<li><a href="https://github.com/johndoe/ecommerce-platform">GitHub Repository</a></li>
</ul>

    </div>
  
    <div class="project-item">
      <p>Task Management App
<strong>Technologies:</strong> Vue.js, Express.js, PostgreSQL</p>
<ul>
<li>Developed collaborative task management application</li>
<li>Features include real-time updates, file sharing, and team collaboration</li>
<li><a href="https://taskapp-demo.com">Live Demo</a> | <a href="https://github.com/johndoe/task-manager">GitHub</a></li>
</ul>

    </div>
  
    <div class="project-item">
      <p>Open Source Library: DataViz
<strong>Technologies:</strong> TypeScript, D3.js, Canvas API</p>
<ul>
<li>Created lightweight data visualization library</li>
<li>500+ GitHub stars, 50+ contributors</li>
<li><a href="https://npmjs.com/package/dataviz-lib">NPM Package</a> | <a href="https://dataviz-docs.com">Documentation</a></li>
</ul>

    </div>
  
                </div>
            </section>

            <section class="links-section">
                <h2>Links 🔗</h2>
                <div class="links-content">
                    
    <div class="link-item">
      <ul>
<li>🐙 <a href="https://github.com/johndoe">GitHub</a></li>
</ul>

    </div>
  
    <div class="link-item">
      <ul>
<li>💼 <a href="https://linkedin.com/in/johndoe">LinkedIn</a></li>
</ul>

    </div>
  
    <div class="link-item">
      <ul>
<li>🐦 <a href="https://twitter.com/johndoe">Twitter</a></li>
</ul>

    </div>
  
    <div class="link-item">
      <ul>
<li>📧 <a href="mailto:<EMAIL>">Email</a></li>
</ul>

    </div>
  
    <div class="link-item">
      <ul>
<li>🌐 <a href="https://johndoe.dev">Personal Website</a></li>
</ul>

    </div>
  
    <div class="link-item">
      <ul>
<li>📝 <a href="https://johndoe.blog">Blog</a></li>
</ul>

    </div>
  
                </div>
            </section>
        </div>
    </div>
</body>
</html>