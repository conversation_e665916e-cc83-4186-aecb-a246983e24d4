/* Retro Portfolio Styles */
:root {
  --retro-green: #00ff41;
  --retro-amber: #ffb000;
  --retro-cyan: #00ffff;
  --retro-pink: #ff1493;
  --retro-purple: #9932cc;
  --dark-bg: #0a0a0a;
  --darker-bg: #050505;
  --terminal-green: #00ff00;
  --retro-font: 'Courier New', 'Monaco', 'Menlo', monospace;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--retro-font);
  background: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 100%);
  color: var(--retro-green);
  line-height: 1.6;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Retro scanlines effect */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 255, 65, 0.03) 2px,
    rgba(0, 255, 65, 0.03) 4px
  );
  pointer-events: none;
  z-index: 1000;
}

#app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
}

.portfolio-container {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--retro-green);
  border-radius: 10px;
  box-shadow:
    0 0 20px var(--retro-green),
    inset 0 0 20px rgba(0, 255, 65, 0.1);
  padding: 2rem;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow:
      0 0 20px var(--retro-green),
      inset 0 0 20px rgba(0, 255, 65, 0.1);
  }
  to {
    box-shadow:
      0 0 30px var(--retro-green),
      0 0 40px var(--retro-green),
      inset 0 0 20px rgba(0, 255, 65, 0.2);
  }
}

/* Hero Section */
.hero-section {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  border-bottom: 2px solid var(--retro-cyan);
}

.profile-photo img {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  border: 3px solid var(--retro-amber);
  box-shadow: 0 0 20px var(--retro-amber);
  margin-bottom: 1rem;
  filter: sepia(20%) hue-rotate(90deg);
}

.name {
  font-size: 3rem;
  color: var(--retro-cyan);
  text-shadow: 0 0 10px var(--retro-cyan);
  margin-bottom: 1rem;
  animation: flicker 3s infinite;
}

@keyframes flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}
