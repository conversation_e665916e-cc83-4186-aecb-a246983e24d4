/* Classic 90s/2000s Portfolio Styles */
body {
  font-family: 'Times New Roman', Times, serif;
  background-color: #ffffff;
  color: #000000;
  margin: 0;
  padding: 20px;
  line-height: 1.4;
  font-size: 14px;
}

#app {
  max-width: 800px;
  margin: 0 auto;
  background-color: #ffffff;
}

.portfolio-container {
  border: 2px solid #000080;
  padding: 20px;
  background-color: #f8f8f8;
}

/* Header Section */
.hero-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #e6e6fa;
  border: 1px solid #9370db;
}

.profile-photo img {
  width: 150px;
  height: 150px;
  border: 3px solid #000080;
  margin-bottom: 15px;
}

.name {
  font-size: 28px;
  color: #000080;
  font-weight: bold;
  margin: 10px 0;
  text-decoration: underline;
}

/* Section Styles */
section {
  margin-bottom: 25px;
  padding: 15px;
  border: 1px solid #cccccc;
  background-color: #ffffff;
}

section h2 {
  color: #800000;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 2px solid #800000;
  text-transform: uppercase;
}

/* Bio Section */
.bio-content {
  font-size: 16px;
  text-align: justify;
  padding: 15px;
  background-color: #fffacd;
  border: 1px dashed #daa520;
  margin: 10px 0;
}

/* Content Items */
.experience-item,
.education-item,
.publication-item,
.project-item {
  margin-bottom: 20px;
  border: 1px solid #808080;
  background-color: #f0f0f0;
  padding: 10px;
}

.experience-item h3,
.education-item h3,
.publication-item h3,
.project-item h3 {
  color: #000080;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  text-decoration: underline;
}

/* Lists */
ul {
  margin-left: 30px;
  margin-bottom: 10px;
}

li {
  margin-bottom: 5px;
  list-style-type: disc;
}

ol {
  margin-left: 30px;
  margin-bottom: 10px;
}

/* Links Section */
.links-content {
  background-color: #e0e0e0;
  border: 2px solid #808080;
  padding: 15px;
}

.link-item {
  display: inline-block;
  margin: 5px 10px;
  padding: 8px 12px;
  background-color: #d3d3d3;
  border: 1px outset #c0c0c0;
  text-align: center;
  min-width: 120px;
}

.link-item:hover {
  background-color: #b0b0b0;
  border: 1px inset #c0c0c0;
}

/* Links and Typography */
a {
  color: #0000ee;
  text-decoration: underline;
}

a:visited {
  color: #551a8b;
}

a:hover {
  color: #ff0000;
  text-decoration: none;
}

p {
  margin-bottom: 10px;
  text-align: justify;
}

strong {
  font-weight: bold;
  color: #000000;
}

em {
  font-style: italic;
}

/* Table Styles for structured content */
table {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
  border: 1px solid #000000;
}

th,
td {
  border: 1px solid #808080;
  padding: 8px;
  text-align: left;
}

th {
  background-color: #c0c0c0;
  font-weight: bold;
}

tr:nth-child(even) {
  background-color: #f0f0f0;
}

/* Classic button style */
.classic-button {
  background-color: #c0c0c0;
  border: 2px outset #c0c0c0;
  padding: 4px 8px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  cursor: pointer;
}

.classic-button:hover {
  background-color: #d0d0d0;
}

.classic-button:active {
  border: 2px inset #c0c0c0;
}

/* Responsive Design for mobile */
@media (max-width: 600px) {
  body {
    padding: 10px;
    font-size: 12px;
  }

  #app {
    max-width: 100%;
  }

  .portfolio-container {
    padding: 15px;
  }

  .name {
    font-size: 22px;
  }

  .profile-photo img {
    width: 120px;
    height: 120px;
  }

  section {
    padding: 10px;
  }

  section h2 {
    font-size: 16px;
  }

  .link-item {
    display: block;
    margin: 5px 0;
    min-width: auto;
  }
}

/* Print styles */
@media print {
  body {
    background-color: white;
    color: black;
    font-size: 12px;
  }

  .portfolio-container {
    border: 1px solid black;
    box-shadow: none;
  }

  .hero-section {
    background-color: white;
    border: 1px solid black;
  }

  section {
    background-color: white;
    border: 1px solid black;
  }

  a {
    color: black;
    text-decoration: underline;
  }
}
